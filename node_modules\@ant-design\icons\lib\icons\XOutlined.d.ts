import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![x](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyMSA5MTJMNjAxLjExIDQ0NS43NWwuNTUuNDNMODkwLjA4IDExMkg3OTMuN0w1NTguNzQgMzg0IDM3Mi4xNSAxMTJIMTE5LjM3bDI5OC42NSA0MzUuMzEtLjA0LS4wNEwxMDMgOTEyaDk2LjM5TDQ2MC42IDYwOS4zOCA2NjguMiA5MTJ6TTMzMy45NiAxODQuNzNsNDQ4LjgzIDY1NC41NEg3MDYuNEwyNTcuMiAxODQuNzN6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
