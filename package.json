{"name": "prefix-front", "version": "1.0.0", "description": "PREFIX 專案現代化重構 - 前端應用程式", "main": "src/index.js", "scripts": {"start": "webpack serve --mode development", "build": "webpack --mode production", "build:dev": "webpack --mode development", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .js,.jsx", "lint:fix": "eslint src --ext .js,.jsx --fix", "format": "prettier --write src/**/*.{js,jsx,css,md}"}, "keywords": ["react", "redux", "prefix", "front"], "author": "PREFIX Development Team", "license": "MIT", "dependencies": {"@ant-design/icons": "^6.0.2", "@reduxjs/toolkit": "^2.9.0", "antd": "^5.3.0", "axios": "^1.3.4", "react": "^19.1.1", "react-dom": "^19.1.1", "react-redux": "^9.2.0", "react-router-dom": "^7.9.2"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.4.3", "babel-loader": "^10.0.0", "buffer": "^6.0.3", "css-loader": "^7.1.2", "eslint": "^9.36.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^5.2.0", "file-loader": "^6.2.0", "glob": "^11.0.0", "html-webpack-plugin": "^5.5.0", "jest": "^30.1.3", "prettier": "^3.6.2", "process": "^0.11.10", "rimraf": "^6.0.1", "style-loader": "^4.0.0", "url-loader": "^4.1.1", "webpack": "^5.76.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["@testing-library/jest-dom"], "moduleNameMapping": {"\\.(css|less|scss|sass)$": "identity-obj-proxy"}}}