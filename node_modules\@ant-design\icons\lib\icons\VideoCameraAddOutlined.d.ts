import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![video-camera-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM2OCA3MjRIMjUyVjYwOGMwLTQuNC0zLjYtOC04LThoLTQ4Yy00LjQgMC04IDMuNi04IDh2MTE2SDcyYy00LjQgMC04IDMuNi04IDh2NDhjMCA0LjQgMy42IDggOCA4aDExNnYxMTZjMCA0LjQgMy42IDggOCA4aDQ4YzQuNCAwIDgtMy42IDgtOFY3ODhoMTE2YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04eiIgLz48cGF0aCBkPSJNOTEyIDMwMi4zTDc4NCAzNzZWMjI0YzAtMzUuMy0yOC43LTY0LTY0LTY0SDEyOGMtMzUuMyAwLTY0IDI4LjctNjQgNjR2MzUyaDcyVjIzMmg1NzZ2NTYwSDQ0OHY3MmgyNzJjMzUuMyAwIDY0LTI4LjcgNjQtNjRWNjQ4bDEyOCA3My43YzIxLjMgMTIuMyA0OC0zLjEgNDgtMjcuNlYzMzBjMC0yNC42LTI2LjctNDAtNDgtMjcuN3pNODg4IDYyNWwtMTA0LTU5LjhWNDU4LjlMODg4IDM5OXYyMjZ6IiAvPjxwYXRoIGQ9Ik0zMjAgMzYwYzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04SDIwOGMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgxMTJ6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
