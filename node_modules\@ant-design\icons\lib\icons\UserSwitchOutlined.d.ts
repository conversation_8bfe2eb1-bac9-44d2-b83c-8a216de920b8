import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![user-switch](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc1OSAzMzVjMC0xMzctMTExLTI0OC0yNDgtMjQ4UzI2MyAxOTggMjYzIDMzNWMwIDgyLjggNDAuNiAxNTYuMiAxMDMgMjAxLjItLjQuMi0uNy4zLS45LjQtNDQuNyAxOC45LTg0LjggNDYtMTE5LjMgODAuNmEzNzMuNDIgMzczLjQyIDAgMDAtODAuNCAxMTkuNUEzNzMuNiAzNzMuNiAwIDAwMTM2IDg3NC44YTggOCAwIDAwOCA4LjJoNTkuOWM0LjMgMCA3LjktMy41IDgtNy44IDItNzcuMiAzMi45LTE0OS41IDg3LjYtMjA0LjNDMzU2IDYxNC4yIDQzMSA1ODMgNTExIDU4M2MxMzcgMCAyNDgtMTExIDI0OC0yNDh6TTUxMSA1MDdjLTk1IDAtMTcyLTc3LTE3Mi0xNzJzNzctMTcyIDE3Mi0xNzIgMTcyIDc3IDE3MiAxNzItNzcgMTcyLTE3MiAxNzJ6bTEwNSAyMjFoMjY0YzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04SDcwMy41bDQ3LjItNjAuMWE4LjEgOC4xIDAgMDAxLjctNC45YzAtNC40LTMuNi04LTgtOGgtNzIuNmMtNC45IDAtOS41IDIuMy0xMi42IDYuMWwtNjguNSA4Ny4xYy00LjQgNS42LTYuOCAxMi42LTYuOCAxOS44LjEgMTcuNyAxNC40IDMyIDMyLjEgMzJ6bTI0MCA2NEg1OTJjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoMTc2LjVsLTQ3LjIgNjAuMWE4LjEgOC4xIDAgMDAtMS43IDQuOWMwIDQuNCAzLjYgOCA4IDhoNzIuNmM0LjkgMCA5LjUtMi4zIDEyLjYtNi4xbDY4LjUtODcuMWM0LjQtNS42IDYuOC0xMi42IDYuOC0xOS44LS4xLTE3LjctMTQuNC0zMi0zMi4xLTMyeiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
