import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![wechat-work](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkwOS43OCA3MjkuNTlhMTM1Ljg3IDEzNS44NyAwIDAwLTQ3LjA0IDE5LjA0IDExNC4yNCAxMTQuMjQgMCAwMS01MS40IDMxLjA4IDc2LjI5IDc2LjI5IDAgMDEyNC40NS00NS40MiAxNjkuMyAxNjkuMyAwIDAwMjMuNC01NS4wMiA1MC40MSA1MC40MSAwIDExNTAuNiA1MC4zMnptLTkyLjIxLTEyMC43NmExNjguODMgMTY4LjgzIDAgMDAtNTQuODEtMjMuNjggNTAuNDEgNTAuNDEgMCAwMS01MC40LTUwLjQyIDUwLjQxIDUwLjQxIDAgMTExMDAuOCAwIDEzNy41IDEzNy41IDAgMDAxOC44MiA0Ny4yIDExNC44IDExNC44IDAgMDEzMC43NiA1MS42NiA3Ni4wOCA3Ni4wOCAwIDAxLTQ1LjAyLTI0Ljc2aC0uMTl6bS04My4wNC0xNzcuNzFjLTE1LjE5LTEyNy4zMy0xNDYuOTgtMjI3LjEtMzA2LjQ0LTIyNy4xLTE2OS44NyAwLTMwOC4wOSAxMTMuMS0zMDguMDkgMjUyLjJBMjM1LjgxIDIzNS44MSAwIDAwMjMwLjA2IDY0Ny42YTMxMS4yOCAzMTEuMjggMCAwMDMzLjYgMjEuNTlMMjUwIDcyMy43NmM0LjkzIDIuMzEgOS43IDQuNzggMTQuNzUgNi45bDY5LTM0LjVjMTAuMDcgMi42MSAyMC42OCA0LjMgMzEuMiA2LjA4IDYuNzMgMS4yIDEzLjQ1IDIuNDMgMjAuMzUgMy4yNWEzNTQuODMgMzU0LjgzIDAgMDAxMjguODEtNy40IDI0OC44OCAyNDguODggMCAwMDEwLjE1IDU1LjA2IDQyNS42NCA0MjUuNjQgMCAwMS05Ni4xNyAxMS4yNCA0MTcuOTggNDE3Ljk4IDAgMDEtODYuNC05LjUyTDIxNi41MiA4MTcuNGEyNy42MiAyNy42MiAwIDAxLTI5Ljk4LTMuMTQgMjguMDIgMjguMDIgMCAwMS05LjY3LTI4LjYxbDIyLjQtOTAuMjRBMjkyLjI2IDI5Mi4yNiAwIDAxNjQgNDU2LjIxQzY0IDI4NS45OCAyMjcgMTQ4IDQyOC4wOSAxNDhjMTkwLjkzIDAgMzQ3LjI5IDEyNC41MyAzNjIuNTIgMjgyLjgyYTI0NC45NyAyNDQuOTcgMCAwMC0yNi40Ny0yLjYyYy05LjkuMzgtMTkuNzkgMS4zMS0yOS42IDIuODh6bS0xMTYuMyAxOTguODFhMTM1Ljc2IDEzNS43NiAwIDAwNDcuMDUtMTkuMDQgMTE0LjI0IDExNC4yNCAwIDAxNTEuNDUtMzEgNzYuNDcgNzYuNDcgMCAwMS0yNC41IDQ1LjM0IDE2OS40OCAxNjkuNDggMCAwMC0yMy40IDU1LjA1IDUwLjQxIDUwLjQxIDAgMDEtMTAwLjguMjMgNTAuNDEgNTAuNDEgMCAwMTUwLjItNTAuNThtOTAuOCAxMjEuMzJhMTY4LjYgMTY4LjYgMCAwMDU0LjY2IDIzLjkgNTAuNDQgNTAuNDQgMCAwMTM1LjY0IDg2LjA4IDUwLjM4IDUwLjM4IDAgMDEtODYuMDQtMzUuNjYgMTM2Ljc0IDEzNi43NCAwIDAwLTE4LjY3LTQ3LjI4IDExNC43MSAxMTQuNzEgMCAwMS0zMC41NC01MS44IDc2IDc2IDAgMDE0NC45NSAyNS4wNnoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
