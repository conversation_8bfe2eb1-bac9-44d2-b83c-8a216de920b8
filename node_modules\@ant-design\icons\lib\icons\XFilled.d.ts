import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![x](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGc+PHBhdGggZD0iTTgyMy4xMSA5MTJIMjAwLjlBODguOSA4OC45IDAgMDExMTIgODIzLjExVjIwMC45QTg4LjkgODguOSAwIDAxMjAwLjg5IDExMkg4MjMuMUE4OC45IDg4LjkgMCAwMTkxMiAyMDAuODlWODIzLjFBODguOSA4OC45IDAgMDE4MjMuMTEgOTEyIiAvPjxwYXRoIGQ9Ik03NDAgNzM1SDU5Ni45NEwyODYgMjkxaDE0My4wNnptLTEyNi4wMS0zNy42NWg1Ni45Nkw0MTIgMzI4LjY1aC01Ni45NnoiIC8+PHBhdGggZD0iTTMzMS4zIDczNUw0OTEgNTQ5LjczIDQ3MC4xMSA1MjIgMjg2IDczNXpNNTIxIDQ2MC4zOUw1NDEuMjEgNDg5IDcxNSAyODloLTQ0LjY3eiIgLz48L2c+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
