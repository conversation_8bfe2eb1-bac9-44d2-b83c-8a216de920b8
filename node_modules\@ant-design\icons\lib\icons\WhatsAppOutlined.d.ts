import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![whats-app](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcxMy41IDU5OS45Yy0xMC45LTUuNi02NS4yLTMyLjItNzUuMy0zNS44LTEwLjEtMy44LTE3LjUtNS42LTI0LjggNS42LTcuNCAxMS4xLTI4LjQgMzUuOC0zNSA0My4zLTYuNCA3LjQtMTIuOSA4LjMtMjMuOCAyLjgtNjQuOC0zMi40LTEwNy4zLTU3LjgtMTUwLTEzMS4xLTExLjMtMTkuNSAxMS4zLTE4LjEgMzIuNC02MC4yIDMuNi03LjQgMS44LTEzLjctMS0xOS4zLTIuOC01LjYtMjQuOC01OS44LTM0LTgxLjktOC45LTIxLjUtMTguMS0xOC41LTI0LjgtMTguOS02LjQtLjQtMTMuNy0uNC0yMS4xLS40LTcuNCAwLTE5LjMgMi44LTI5LjQgMTMuNy0xMC4xIDExLjEtMzguNiAzNy44LTM4LjYgOTJzMzkuNSAxMDYuNyA0NC45IDExNC4xYzUuNiA3LjQgNzcuNyAxMTguNiAxODguNCAxNjYuNSA3MCAzMC4yIDk3LjQgMzIuOCAxMzIuNCAyNy42IDIxLjMtMy4yIDY1LjItMjYuNiA3NC4zLTUyLjUgOS4xLTI1LjggOS4xLTQ3LjkgNi40LTUyLjUtMi43LTQuOS0xMC4xLTcuNy0yMS0xM3oiIC8+PHBhdGggZD0iTTkyNS4yIDMzOC40Yy0yMi42LTUzLjctNTUtMTAxLjktOTYuMy0xNDMuM2E0NDQuMzUgNDQ0LjM1IDAgMDAtMTQzLjMtOTYuM0M2MzAuNiA3NS43IDU3Mi4yIDY0IDUxMiA2NGgtMmMtNjAuNi4zLTExOS4zIDEyLjMtMTc0LjUgMzUuOWE0NDUuMzUgNDQ1LjM1IDAgMDAtMTQyIDk2LjVjLTQwLjkgNDEuMy03MyA4OS4zLTk1LjIgMTQyLjgtMjMgNTUuNC0zNC42IDExNC4zLTM0LjMgMTc0LjlBNDQ5LjQgNDQ5LjQgMCAwMDExMiA3MTR2MTUyYTQ2IDQ2IDAgMDA0NiA0NmgxNTIuMUE0NDkuNCA0NDkuNCAwIDAwNTEwIDk2MGgyLjFjNTkuOSAwIDExOC0xMS42IDE3Mi43LTM0LjNhNDQ0LjQ4IDQ0NC40OCAwIDAwMTQyLjgtOTUuMmM0MS4zLTQwLjkgNzMuOC04OC43IDk2LjUtMTQyIDIzLjYtNTUuMiAzNS42LTExMy45IDM1LjktMTc0LjUuMy02MC45LTExLjUtMTIwLTM0LjgtMTc1LjZ6bS0xNTEuMSA0MzhDNzA0IDg0NS44IDYxMSA4ODQgNTEyIDg4NGgtMS43Yy02MC4zLS4zLTEyMC4yLTE1LjMtMTczLjEtNDMuNWwtOC40LTQuNUgxODhWNjk1LjJsLTQuNS04LjRDMTU1LjMgNjMzLjkgMTQwLjMgNTc0IDE0MCA1MTMuN2MtLjQtOTkuNyAzNy43LTE5My4zIDEwNy42LTI2My44IDY5LjgtNzAuNSAxNjMuMS0xMDkuNSAyNjIuOC0xMDkuOWgxLjdjNTAgMCA5OC41IDkuNyAxNDQuMiAyOC45IDQ0LjYgMTguNyA4NC42IDQ1LjYgMTE5IDgwIDM0LjMgMzQuMyA2MS4zIDc0LjQgODAgMTE5IDE5LjQgNDYuMiAyOS4xIDk1LjIgMjguOSAxNDUuOC0uNiA5OS42LTM5LjcgMTkyLjktMTEwLjEgMjYyLjd6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
